// frontend/src/components/VideoPlayer.js
import React, { forwardRef } from 'react';

const VideoPlayer = forwardRef(({ videoSrc, subtitleUrl, onTimeUpdate, onLoadedMetadata }, ref) => {
  return (
    <video 
      ref={ref} 
      controls 
      width="100%" 
      onTimeUpdate={onTimeUpdate}
      onLoadedMetadata={onLoadedMetadata}
      crossOrigin="anonymous" // Important if subtitles/video are from different origin
    >
      <source src={videoSrc} type="video/mp4" /> {/* Adjust type if needed, e.g. video/webm */}
      {subtitleUrl && <track label="English" kind="subtitles" srcLang="en" src={subtitleUrl} default />}
      Your browser does not support the video tag.
    </video>
  );
});

export default VideoPlayer;
