// frontend/src/pages/PlayerPage.js
import React, { useState, useEffect, useRef, useCallback } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import VideoPlayer from '../components/VideoPlayer.jsx';
import SubtitleDisplay from '../components/SubtitleDisplay.jsx';
import ESLAudioControls from '../components/ESLAudioControls.jsx';
import apiClient from '../services/apiClient';

// Helper to parse VTT (very basic) or use segments directly
const parseVTT = (vttString) => {
  if (!vttString) return [];
  const lines = vttString.split('\n');
  const segments = [];
  let currentSegment = null;

  for (const line of lines) {
    if (line.includes('-->')) {
      if (currentSegment) segments.push(currentSegment); // Should not happen if format is good
      const [startStr, endStr] = line.split(' --> ');
      currentSegment = {
        start: timeStringToSeconds(startStr),
        end: timeStringToSeconds(endStr),
        text: ''
      };
    } else if (currentSegment && line.trim() !== '' && !/^[0-9]+$/.test(line.trim())) {
      currentSegment.text += (currentSegment.text ? ' ' : '') + line.trim();
    } else if (line.trim() === '' && currentSegment) {
      if (currentSegment.text) segments.push(currentSegment);
      currentSegment = null;
    }
  }
  if (currentSegment && currentSegment.text) segments.push(currentSegment);
  return segments;
};

const timeStringToSeconds = (timeStr) => {
  const parts = timeStr.split(':');
  let seconds = 0;
  if (parts.length === 3) { // HH:MM:SS.mmm
    seconds += parseInt(parts[0], 10) * 3600;
    seconds += parseInt(parts[1], 10) * 60;
    seconds += parseFloat(parts[2]);
  } else if (parts.length === 2) { // MM:SS.mmm
    seconds += parseInt(parts[0], 10) * 60;
    seconds += parseFloat(parts[1]);
  }
  return seconds;
};


function PlayerPage() {
  const { jobId } = useParams(); // This is Replicate's prediction ID
  const navigate = useNavigate();
  const [jobStatus, setJobStatus] = useState(null);
  const [videoSrc, setVideoSrc] = useState('');
  const [subtitleUrl, setSubtitleUrl] = useState(''); // URL to .vtt file from Replicate
  const [segments, setSegments] = useState([]); // Parsed from Replicate output.segments or VTT
  const [currentSegmentText, setCurrentSegmentText] = useState('');
  const [currentActiveSegment, setCurrentActiveSegment] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [errorMessage, setErrorMessage] = useState('');

  const videoRef = useRef(null);
  const pollIntervalRef = useRef(null);

  const fetchJobStatus = useCallback(async () => {
    try {
      const response = await apiClient.get(`/transcribe/status/${jobId}`);
      const data = response.data;
      setJobStatus(data.status);

      if (data.status === 'succeeded') {
        setIsLoading(false);
        setVideoSrc(data.output?.audio_path || data.input?.audio); // Replicate output might have processed audio path or use input

        // Prefer direct VTT URL if available from Replicate output
        if (data.output?.vtt) {
            setSubtitleUrl(data.output.vtt); // This is a URL
             // Fetch and parse VTT if direct URL is given, to also populate segments for ESL controls
            fetch(data.output.vtt).then(res => res.text()).then(vttText => {
                setSegments(parseVTT(vttText));
            }).catch(err => console.error("Error fetching/parsing VTT from URL:", err));

        } else if (data.output?.segments) {
            setSegments(data.output.segments); // Assuming segments have {start, end, text}
            // If we only have segments, we might need to generate a VTT blob URL client-side
            // For now, let's assume VideoPlayer can handle segments or we generate VTT if needed
            const vttBlob = new Blob([generateVTTFromSegments(data.output.segments)], {type: 'text/vtt'});
            setSubtitleUrl(URL.createObjectURL(vttBlob));
        } else {
            console.warn("No VTT URL or segments found in Replicate output.");
            setErrorMessage("Transcription complete, but subtitle data is missing.");
        }

        if (pollIntervalRef.current) clearInterval(pollIntervalRef.current);
      } else if (data.status === 'failed' || data.status === 'canceled') {
        setIsLoading(false);
        setErrorMessage(`Transcription job ${data.status}: ${data.error || 'Unknown error'}`);
        if (pollIntervalRef.current) clearInterval(pollIntervalRef.current);
      }
      // If still processing, the interval will continue polling.
    } catch (error) {
      console.error('Error fetching transcription status:', error);
      setIsLoading(false);
      setErrorMessage('Failed to fetch transcription status.');
      if (pollIntervalRef.current) clearInterval(pollIntervalRef.current);
    }
  }, [jobId]);

  useEffect(() => {
    fetchJobStatus(); // Initial fetch
    pollIntervalRef.current = setInterval(fetchJobStatus, 5000); // Poll every 5 seconds
    return () => {
      if (pollIntervalRef.current) clearInterval(pollIntervalRef.current);
      if(subtitleUrl && subtitleUrl.startsWith('blob:')) {
        URL.revokeObjectURL(subtitleUrl); // Clean up blob URL
      }
    };
  }, [fetchJobStatus, subtitleUrl]);

  const handleTimeUpdate = () => {
    if (videoRef.current && segments.length > 0) {
      const currentTime = videoRef.current.currentTime;
      const activeSegment = segments.find(seg => currentTime >= seg.start && currentTime < seg.end);
      if (activeSegment) {
        setCurrentSegmentText(activeSegment.text);
        setCurrentActiveSegment(activeSegment);
      } else {
        setCurrentSegmentText('');
        setCurrentActiveSegment(null);
      }
    }
  };

  const generateVTTFromSegments = (segArray) => {
    let vtt = "WEBVTT\n\n";
    segArray.forEach((s, i) => {
        vtt += `${i+1}\n`;
        vtt += `${timeSecondsToVTTCustom(s.start)} --> ${timeSecondsToVTTCustom(s.end)}\n`;
        vtt += `${s.text}\n\n`;
    });
    return vtt;
  };

  const timeSecondsToVTTCustom = (timeInSeconds) => {
    const hours = Math.floor(timeInSeconds / 3600);
    const minutes = Math.floor((timeInSeconds % 3600) / 60);
    const seconds = Math.floor(timeInSeconds % 60);
    const milliseconds = Math.floor((timeInSeconds * 1000) % 1000);
    return `${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}.${String(milliseconds).padStart(3, '0')}`;
  };


  const handleRepeatSegment = () => {
    if (videoRef.current && currentActiveSegment) {
      videoRef.current.currentTime = currentActiveSegment.start;
      videoRef.current.play();
    }
  };

  if (isLoading) return <p>Loading transcription data (Job ID: {jobId})... Status: {jobStatus || 'Initializing'}</p>;
  if (errorMessage) return <p style={{color: 'red'}}>Error: {errorMessage} <button onClick={() => navigate('/upload')}>Try another upload</button></p>;
  if (!videoSrc) return <p>Transcription succeeded, but video source is unavailable.</p>;

  return (
    <div>
      <h2>Video Player (Job: {jobId})</h2>
      <VideoPlayer
        ref={videoRef}
        videoSrc={videoSrc}
        subtitleUrl={subtitleUrl}
        onTimeUpdate={handleTimeUpdate}
      />
      <SubtitleDisplay currentSegmentText={currentSegmentText} />
      <ESLAudioControls
        videoElement={videoRef.current}
        currentSegment={currentActiveSegment}
        onRepeatSegment={handleRepeatSegment}
      />
      {/* For debugging: */}
      {/* <pre>Segments: {JSON.stringify(segments, null, 2)}</pre> */}
    </div>
  );
}

export default PlayerPage;
